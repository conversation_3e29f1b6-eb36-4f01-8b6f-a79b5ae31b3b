import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../soma_ui.dart';

class SMRulesModal extends StatelessWidget {
  final String title;
  final String description;
  final String? coupon;
  final String buttonText;
  final VoidCallback onTapButton;
  final TextStyle? titleStyle;
  final TextStyle? descriptionStyle;

  const SMRulesModal({
    super.key,
    required this.title,
    required this.description,
    required this.buttonText,
    required this.onTapButton,
    this.coupon,
    this.titleStyle,
    this.descriptionStyle,
  });

  static void show({
    required BuildContext context,
    required String title,
    required String description,
    required String buttonText,
    required VoidCallback onTapButton,
    String? coupon,
    TextStyle? titleStyle,
    TextStyle? descriptionStyle,
  }) {
    Get.bottomSheet(
      SizedBox(
        height: MediaQuery.of(context).size.height * 0.8,
        child: SMRulesModal(
          title: title,
          description: description,
          coupon: coupon,
          buttonText: buttonText,
          onTapButton: onTapButton,
          titleStyle: titleStyle,
          descriptionStyle: descriptionStyle,
        ),
      ),
      isScrollControlled: true,
      enableDrag: true,
      isDismissible: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final colors = tokens.colors;
    final typography = tokens.typography;
    final spacingStack = tokens.spacingStack;
    final spacingInset = tokens.spacingInset;
    final spacingInline = tokens.spacingInline;
    final icons = tokens.icons;

    final hasCoupon = coupon?.isNotEmpty == true;

    return SMBottomSheet(
      title: title,
      titleStyle: titleStyle ??
          TextStyle(
            fontFamily: typography.fontFamilyPrimary,
            fontSize: typography.fontSizes.md,
            height: typography.lineHeight.md / typography.fontSizes.md,
            color: colors.typography.pure2,
          ),
      hideButton: true,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            description,
            style: descriptionStyle ??
                typography.typeStyles.body.copyWith(
                  color: colors.typography.pure2,
                ),
          ),
          if (hasCoupon) ...[
            SizedBox(height: spacingStack.md),
            _buildCouponContainer(
              coupon: coupon!,
              tokens: tokens,
              colors: colors,
              typography: typography,
              spacingStack: spacingStack,
              spacingInset: spacingInset,
              spacingInline: spacingInline,
              icons: icons,
              designTokens: tokens,
            ),
          ],
          SizedBox(height: spacingStack.md),
          SMButton.primary(
            style: const SMButtonStyle(
              borderRadius: BorderRadius.zero,
            ),
            child: Text(buttonText),
            onPressed: () {
              Navigator.pop(context);
              onTapButton();
            },
            expanded: true,
          )
        ],
      ),
    );
  }

  Widget _buildCouponContainer({
    required String coupon,
    required DesignTokens tokens,
    required DTColors colors,
    required DTTypography typography,
    required DTSpacingStack spacingStack,
    required DTSpacingInset spacingInset,
    required DTSpacingInline spacingInline,
    required DTIcons icons,
    required DesignTokens designTokens,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: () => _copyCoupon(
            coupon: coupon,
            colors: colors,
            spacingInset: spacingInset,
            designTokens: designTokens,
          ),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(spacingInset.md),
            decoration: BoxDecoration(
              border: DashedBorder(color: colors.neutral.medium1),
              borderRadius: BorderRadius.zero,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'CUPOM $coupon',
                  style: typography.typeStyles.body.copyWith(
                    color: colors.typography.pure2,
                    fontSize: typography.fontSizes.xus,
                    fontWeight: FontWeight.normal,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  icons.copy,
                  size: 18,
                  color: colors.typography.pure2,
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: spacingStack.xs),
        Text(
          'Copiar cupom',
          style: typography.typeStyles.body.copyWith(
            color: colors.typography.pure2,
            fontSize: typography.fontSizes.xxus,
          ),
        ),
      ],
    );
  }

  void _copyCoupon({
    required String coupon,
    required DTColors colors,
    required DTSpacingInset spacingInset,
    required DesignTokens designTokens,
  }) {
    Clipboard.setData(ClipboardData(text: coupon));
    // Feedback visual
    showSnackBar(SMSnackBarGetX(
      textLabel: 'Cupom $coupon copiado para a área de transferência',
      backgroundColor: colors.feedback.pureSuccess,
      textColor: colors.neutral.pure1,
      duration: 2,
      designTokens: designTokens,
      borderRadiusSnack: 0,
    ));
  }
}
